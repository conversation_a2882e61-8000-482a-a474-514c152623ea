import ezdxf
import json
import re
import os
import math
from pathlib import Path
from collections import defaultdict
from dataclasses import dataclass, field
from typing import List, Dict, Any, Tuple
from ezdxf.math import BoundingBox, Vec3
from tqdm import tqdm

# --- 配置常量 ---
# 用于判断线条是否水平/垂直的容差 (单位：图形单位)
GEOMETRY_TOLERANCE = 1.0
# 用于判断是否为同一个表格的行/列坐标的合并容差
TABLE_GRID_TOLERANCE = 5.0
# 图纸外框的最小尺寸，小于此尺寸的矩形不会被当做图纸外框
MIN_SHEET_DIMENSION = 1000.0

# --- 关键词常量 ---
KEYWORD_LEGEND = ["图例", "说明", "符号", "标识", "图标", "标记", "图示"]
KEYWORD_TABLE = ["工程号", "项目号", "图号", "设计", "审核", "校对", "批准", "比例", "日期", "版本", "清单", "材料",
                 "设备", "参数"]


@dataclass
class TextEntity:
    """结构化的文本实体"""
    content: str
    pos: Vec3
    height: float = 0.0
    layer: str = "0"


@dataclass
class Table:
    """结构化的表格对象"""
    bbox: BoundingBox
    rows: int = 0
    cols: int = 0
    data: List[List[TextEntity]] = field(default_factory=list)
    title: str = "未命名表格"
    category: str = "数据表格"  # '数据表格', '图例表格', '工程表格'

    def get_full_text(self) -> str:
        """获取表格内所有文本内容的拼接"""
        return " ".join(cell.content for row in self.data for cell in row if cell)


@dataclass
class Sheet:
    """结构化的图纸对象"""
    name: str
    bbox: BoundingBox
    tables: List[Table] = field(default_factory=list)
    main_drawing_entities: List[Dict] = field(default_factory=list)
    legend_entities: List[Dict] = field(default_factory=list)
    unassigned_entities: List[Dict] = field(default_factory=list)


class DXFStructureParser:
    """
    DXF图纸结构化解析器 V4.1 - 几何优先版

    核心逻辑:
    1. 基于大型矩形框识别图纸 (Sheet)。
    2. 在图纸内识别由横平竖直线构成的网格，重建为表格 (Table)。
    3. 基于关键词对表格进行分类。
    4. 将其他实体分配到主图或图例区域。
    """

    def __init__(self, dxf_path: str):
        self.dxf_path = dxf_path
        self.doc = None
        self.msp = None
        self.all_entities = []

    def parse(self) -> Dict[str, Any]:
        """执行完整的解析流程"""
        try:
            self.doc = ezdxf.readfile(self.dxf_path)
            self.msp = self.doc.modelspace()
        except Exception as e:
            return {"错误": f"无法读取DXF文件: {e}"}

        # 1. 提取所有需要的实体
        self._extract_all_entities()

        # 2. 检测图纸区域
        sheets = self._detect_sheets()
        if not sheets:
            # 如果没有找到明确的图框，则将整个绘图区域视为一张图纸
            try:
                overall_bbox = self.msp.graphics_extents()
                if overall_bbox.has_data:
                    sheets.append(Sheet(name="主图纸", bbox=overall_bbox))
            except Exception:
                # 如果模型空间为空或无法计算边界，则返回错误
                pass

        if not sheets:
            return {"错误": "无法确定图纸边界，且图纸为空。"}

        # 3. 在每张图纸内重建表格并分配实体
        for sheet in sheets:
            # a. 重建表格
            sheet.tables = self._reconstruct_tables_in_sheet(sheet)
            # b. 分配其他实体
            self._assign_entities_to_sheet(sheet)

        # 4. 生成最终的结构化输出
        return self._generate_final_output(sheets)

    def _extract_all_entities(self):
        """从模型空间提取并初步分类实体"""
        self.lines = []
        self.polylines = []
        self.texts = []

        for entity in self.msp:
            etype = entity.dxftype()
            if etype == 'LINE':
                self.lines.append(entity)
            elif etype == 'LWPOLYLINE':
                self.polylines.append(entity)
            elif etype in ('TEXT', 'MTEXT'):
                self._extract_text(entity)

    def _extract_text(self, entity):
        """提取并清洗文本"""
        try:
            if entity.dxftype() == 'MTEXT':
                # 移除MTEXT格式化代码
                text_content = re.sub(r'\\[A-Za-z][0-9]*;?|\\{|\\}|\\P', ' ', entity.text).strip()
                pos = entity.dxf.insert
                height = entity.dxf.char_height
            else:  # TEXT
                text_content = entity.dxf.text.strip()
                pos = entity.dxf.insert
                height = entity.dxf.height

            if text_content:
                self.texts.append(TextEntity(
                    content=text_content,
                    pos=pos,
                    height=height,
                    layer=entity.dxf.layer
                ))
        except Exception:
            # 忽略无法解析的文本实体
            pass

    def _detect_sheets(self) -> List[Sheet]:
        """通过检测大型矩形图框来识别图纸"""
        sheets = []
        for pline in self.polylines:
            if pline.is_closed:
                # 错误修正：pline.points 是一个方法，需要调用它
                points = pline.points()
                # 检查是否为矩形
                if len(points) == 4 or (len(points) == 5 and points[0].isclose(points[-1])):
                    try:
                        # 错误修正：使用获取到的 points 列表创建包围盒
                        bbox = BoundingBox(points)
                        # 检查尺寸是否满足最小图纸要求
                        if bbox.size.x > MIN_SHEET_DIMENSION and bbox.size.y > MIN_SHEET_DIMENSION:
                            sheets.append(Sheet(name="", bbox=bbox))
                    except Exception:
                        continue

        # 按X坐标排序图纸
        sheets.sort(key=lambda s: s.bbox.extmin.x)
        for i, sheet in enumerate(sheets):
            sheet.name = f"图纸{i + 1}"

        if len(sheets) == 1:
            sheets[0].name = "主图纸"

        return sheets

    def _reconstruct_tables_in_sheet(self, sheet: Sheet) -> List[Table]:
        """在单个图纸区域内，通过几何网格线重建所有表格"""
        # 筛选出在图纸范围内的横平竖直线
        h_lines, v_lines = [], []
        for line in self.lines:
            if sheet.bbox.inside(line.dxf.start) and sheet.bbox.inside(line.dxf.end):
                p1, p2 = line.dxf.start, line.dxf.end
                if abs(p1.y - p2.y) < GEOMETRY_TOLERANCE:
                    h_lines.append(p1.y)
                elif abs(p1.x - p2.x) < GEOMETRY_TOLERANCE:
                    v_lines.append(p1.x)

        if not h_lines or not v_lines:
            return []

        # 对坐标进行去重和排序
        y_coords = sorted(list(set(h_lines)), reverse=True)
        x_coords = sorted(list(set(v_lines)))

        # 合并非常接近的线 (处理粗线框)
        y_grid = self._merge_close_coords(y_coords, TABLE_GRID_TOLERANCE)
        x_grid = self._merge_close_coords(x_coords, TABLE_GRID_TOLERANCE)

        # 此处可以加入更复杂的算法来分割多个独立的表格网格
        # 为简化，我们暂时将所有网格视为一个大表格进行处理
        if len(y_grid) < 2 or len(x_grid) < 2:
            return []

        # 创建表格并填充数据
        num_rows, num_cols = len(y_grid) - 1, len(x_grid) - 1
        table_bbox = BoundingBox((x_grid[0], y_grid[-1]), (x_grid[-1], y_grid[0]))
        table = Table(bbox=table_bbox, rows=num_rows, cols=num_cols)
        table.data = [[None for _ in range(num_cols)] for _ in range(num_rows)]

        # 将文本实体放入对应的单元格
        sheet_texts = [text for text in self.texts if sheet.bbox.inside(text.pos)]
        for text in sheet_texts:
            if not table.bbox.inside(text.pos): continue

            row_idx, col_idx = -1, -1
            for i in range(num_rows):
                if y_grid[i + 1] <= text.pos.y <= y_grid[i]:
                    row_idx = i
                    break
            for j in range(num_cols):
                if x_grid[j] <= text.pos.x <= x_grid[j + 1]:
                    col_idx = j
                    break

            if row_idx != -1 and col_idx != -1:
                # 如果单元格已有内容，则追加
                if table.data[row_idx][col_idx]:
                    table.data[row_idx][col_idx].content += f" {text.content}"
                else:
                    table.data[row_idx][col_idx] = text

        # 分类表格
        table_text = table.get_full_text()
        if any(kw in table_text for kw in KEYWORD_LEGEND):
            table.category = "图例表格"
            table.title = "图例说明"
        elif any(kw in table_text for kw in KEYWORD_TABLE):
            table.category = "工程表格"
            table.title = "工程信息表"
        else:
            # 尝试从第一行文字推断标题
            first_row_text = "".join(c.content for c in table.data[0] if c)
            if first_row_text:
                table.title = first_row_text[:20]  # 取前20个字符作标题

        return [table]

    def _merge_close_coords(self, coords: List[float], tolerance: float) -> List[float]:
        """合并排序后列表中的相近值"""
        if not coords:
            return []
        merged = [coords[0]]
        for i in range(1, len(coords)):
            if abs(coords[i] - merged[-1]) > tolerance:
                merged.append(coords[i])
        return merged

    def _assign_entities_to_sheet(self, sheet: Sheet):
        """将实体分类到图纸的主图、图例等区域"""
        # 获取已分配到表格的实体位置
        table_entity_positions = {t.pos for table in sheet.tables for row in table.data for t in row if t}

        for text in self.texts:
            if not sheet.bbox.inside(text.pos) or text.pos in table_entity_positions:
                continue

            entity_info = {"类型": "文本", "内容": text.content, "图层": text.layer}
            # 基于文本内容和图层名进行简单分类
            if any(kw in text.content or kw in text.layer for kw in KEYWORD_LEGEND):
                sheet.legend_entities.append(entity_info)
            else:
                sheet.main_drawing_entities.append(entity_info)

        # 分配非文本的几何图形 (简化)
        # 此处可以添加更复杂的逻辑来分配线、圆等
        sheet.main_drawing_entities.append(
            {"类型": "统计", "内容": f"包含 {len(self.lines)} 条线、{len(self.polylines)} 条多段线等几何图形"})

    def _generate_final_output(self, sheets: List[Sheet]) -> Dict[str, Any]:
        """构建最终的JSON和Markdown输出"""
        output = {
            "文件元数据": {
                "文件名": os.path.basename(self.dxf_path),
                "DXF版本": self.doc.dxfversion,
                "图纸数量": len(sheets)
            },
            "图纸结构": []
        }

        markdown_output = [f"# DXF解析报告: {os.path.basename(self.dxf_path)}\n"]

        for sheet in sheets:
            sheet_data = {
                "图纸名称": sheet.name,
                "主图内容": sheet.main_drawing_entities,
                "图例内容": sheet.legend_entities,
                "表格列表": []
            }
            markdown_output.append(f"## {sheet.name}\n")

            if not sheet.tables:
                markdown_output.append("此图纸未检测到表格。\n")
            else:
                for table in sheet.tables:
                    table_json, table_md = self._format_table(table)
                    sheet_data["表格列表"].append(table_json)
                    markdown_output.append(table_md)

            output["图纸结构"].append(sheet_data)

        output["markdown报告"] = "".join(markdown_output)
        return output

    def _format_table(self, table: Table) -> Tuple[Dict, str]:
        """将单个表格格式化为JSON和Markdown"""
        # 准备JSON数据
        json_data = {
            "表格标题": table.title,
            "表格类型": table.category,
            "行数": table.rows,
            "列数": table.cols,
            "数据": [
                [cell.content if cell else "" for cell in row]
                for row in table.data
            ]
        }

        # 准备Markdown数据
        headers = [f"列 {i + 1}" for i in range(table.cols)]
        md_lines = [
            f"### {table.title} ({table.category})\n",
            "| " + " | ".join(headers) + " |",
            "| " + " | ".join(["---"] * len(headers)) + " |"
        ]
        for row_data in json_data["数据"]:
            cleaned_row = [str(cell).replace("|", "\\|") for cell in row_data]
            md_lines.append("| " + " | ".join(cleaned_row) + " |")

        md_lines.append("\n")

        return json_data, "\n".join(md_lines)


def main():
    """主执行函数"""
    # --- 请在这里配置您的输入和输出路径 ---
    # 输入可以是单个DXF文件，也可以是一个包含DXF文件的文件夹
    # 示例: input_path = 'C:/Users/<USER>/Desktop/dxf_drawings'
    # 示例: input_path = 'C:/Users/<USER>/Desktop/single_drawing.dxf'
    input_path = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test'
    # --- 配置结束 ---

    input_path = Path(input_path)
    if not input_path.exists():
        print(f"错误: 输入路径不存在 -> {input_path}")
        return

    if input_path.is_dir():
        output_dir = input_path.parent / f"{input_path.name}_parsed_results"
        dxf_files = list(input_path.glob('*.dxf')) + list(input_path.glob('*.DXF'))
    else:
        output_dir = input_path.parent / f"{input_path.stem}_parsed_results"
        dxf_files = [input_path]

    output_dir.mkdir(exist_ok=True)

    if not dxf_files:
        print(f"错误: 在路径 {input_path} 中未找到任何DXF文件。")
        return

    print(f"找到 {len(dxf_files)} 个DXF文件，结果将保存在: {output_dir}")

    for dxf_file in tqdm(dxf_files, desc="正在解析DXF文件"):
        parser = DXFStructureParser(str(dxf_file))
        result = parser.parse()

        # 生成输出文件名
        json_output_path = output_dir / f"{dxf_file.stem}_structure.json"
        md_output_path = output_dir / f"{dxf_file.stem}_report.md"

        if "错误" in result:
            print(f"\n[失败] 解析: {dxf_file.name} - {result['错误']}")
            continue

        try:
            # 提取Markdown报告并保存
            markdown_report = result.pop("markdown报告", "生成报告失败。")
            with open(md_output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_report)

            # 保存剩余的JSON数据
            with open(json_output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            # print(f"\n[成功] 解析: {dxf_file.name}")
        except Exception as e:
            print(f"\n[失败] 保存结果时出错: {dxf_file.name} - {e}")

    print("\n所有文件处理完毕。")


if __name__ == '__main__':
    main()